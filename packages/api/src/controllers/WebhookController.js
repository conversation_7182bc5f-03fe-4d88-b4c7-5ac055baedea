const { get, assign, find, includes, isEmpty } = require('lodash');
const moment = require('moment');
const { errorHandler } = require('../utils/errorHandler');
const Stripe = require('../../config/stripe');
const SubscriptionServices = require('../services/Subscription');
const UsersController = require('./UsersController');
const PlanController = require('./PlanController');
const subscriptionServices = new SubscriptionServices();
const CRMService = require('../services/CRMService');
const ProjectService = require('../services/ProjectService');

class WebhookController {
  /**
   * To handle the stripe webhook
   *
   * @param {Hapi request obj} request - request object
   * @param {hapi handler} h - hapi handler object
   */
  static async stripeWebhook(request, h) {
    try {
      request.logger.info('WebhookController.stripeWebhook called!');
      const { payload, headers } = request;
      let event;
      const stripe = Stripe.get();

      // Verify the webhook signature
      try {
        event = stripe.webhooks.constructEvent(
          payload,
          headers['stripe-signature'],
          process.env.STRIPE_WEBHOOK_SECRET,
        );
      } catch (err) {
        request.logger.error(err, 'Webhook signature verification failed.');
        return h
          .response({
            statusCode: 409,
            error: 'bad_request',
            message: 'Webhook signature verification failed',
          })
          .code(409);
      }

      request.logger.info('WebhookController.stripeWebhook event', event);
      /* Handle events */
      switch (event.type) {
        case 'customer.subscription.deleted': {
          const eventData = event.data.object;
          await WebhookController.handleSubscriptionDeleted(request, eventData);
          break;
        }
        case 'customer.subscription.updated': {
          const subscription = event.data.object;
          const previousAttributes = event.data.previous_attributes || {};
          await WebhookController.updateSubscription(
            request,
            subscription,
            previousAttributes,
          );
          break;
        }
        // case 'invoice.payment_failed': {
        //   const eventData = event.data.object;
        //   // await this.handleInvoicePaymentFailedEvent(eventData);
        //   break;
        // }
        // case 'payment_intent.succeeded': {
        //   const eventData = event.data.object;
        //   await WebhookController.handlePaymentIntentSucceededEvent(
        //     request,
        //     eventData,
        //   );
        //   break;
        // }
        case 'checkout.session.completed': {
          const eventData = event.data.object;
          await WebhookController.handleCheckoutSession(request, eventData);
          break;
        }
        // case 'invoice.created': {
        //   const eventData = event.data.object;
        //   const stripe = Stripe.get();
        //   const requestLog = {
        //     invoiceId: eventData.id,
        //     subscriptionId: eventData.subscription,
        //   };
        //   request.logger.info('invoice.created event received', requestLog);

        //   // Helper function for loose period match
        //   function periodsRoughlyMatch(aStart, aEnd, bStart, bEnd, buffer = 5) {
        //     return (
        //       Math.abs(aStart - bStart) <= buffer ||
        //       Math.abs(aEnd - bEnd) <= buffer ||
        //       Math.max(aStart, bStart) < Math.min(aEnd, bEnd) + buffer
        //     );
        //   }

        //   if (eventData.status === 'draft' && eventData.subscription) {
        //     // Always try to finalize the draft invoice first
        //     let finalizedInvoice = null;
        //     try {
        //       finalizedInvoice = await stripe.invoices.finalizeInvoice(
        //         eventData.id,
        //       );
        //       request.logger.info(
        //         `Invoice finalized: ${finalizedInvoice.id}, status: ${finalizedInvoice.status}`,
        //       );
        //     } catch (err) {
        //       request.logger.error(
        //         `Error finalizing invoice: ${eventData.id}`,
        //         err,
        //       );
        //       // If cannot finalize, exit early (Stripe may not allow immediate finalization)
        //       break;
        //     }

        //     // After finalization, check for duplicate/overlap
        //     const subscription = await stripe.subscriptions.retrieve(
        //       eventData.subscription,
        //     );
        //     const now = Math.floor(Date.now() / 1000);

        //     if (
        //       subscription.status === 'active' &&
        //       subscription.trial_end &&
        //       subscription.trial_end < now
        //     ) {
        //       const invoices = await stripe.invoices.list({
        //         subscription: subscription.id,
        //         limit: 10,
        //       });

        //       request.logger.info(
        //         'All invoices for subscription:',
        //         invoices.data,
        //       );

        //       const latestPaidInvoice = invoices.data.find(
        //         (inv) => inv.status === 'paid',
        //       );
        //       request.logger.info('Latest paid invoice:', latestPaidInvoice);

        //       for (const invoice of invoices.data) {
        //         request.logger.info('Checking finalized invoice:', invoice);

        //         if (
        //           subscription.status === 'active' &&
        //           invoice.status === 'open' // finalized invoices are 'open'
        //         ) {
        //           if (
        //             latestPaidInvoice &&
        //             periodsRoughlyMatch(
        //               latestPaidInvoice.period_start,
        //               latestPaidInvoice.period_end,
        //               invoice.period_start,
        //               invoice.period_end,
        //             )
        //           ) {
        //             request.logger.info(
        //               `Loose period match found, voiding unwanted open invoice. Checking if period_start < now: ${invoice.period_start} < ${now}`,
        //             );
        //             if (invoice.period_start < now) {
        //               request.logger.info(
        //                 `Voiding unwanted open invoice: ${invoice.id}`,
        //               );
        //               try {
        //                 await stripe.invoices.voidInvoice(invoice.id);
        //                 request.logger.info(
        //                   `Voided unwanted open invoice: ${invoice.id}`,
        //                 );
        //               } catch (err) {
        //                 request.logger.error(
        //                   `Error voiding invoice: ${invoice.id}`,
        //                   err,
        //                 );
        //               }
        //             } else {
        //               request.logger.info(
        //                 'Open invoice period is in the future, not voiding.',
        //               );
        //             }
        //           } else {
        //             request.logger.info(
        //               'Open invoice period does NOT loosely match any paid invoice.',
        //             );
        //           }
        //         }
        //       }
        //     }
        //   }
        //   break;
        // }
        // case 'customer.subscription.updated': {
        //   const eventData = event.data.object;
        //   await WebhookController.handleSubscriptionUpdatedEvent(request, event);
        //   break;
        // }
        // case 'invoice.created': {
        //   const eventData = event.data.object;
        //   // await this.handleInvoiceDraftDetele(eventData);
        //   break;
        // }
        case 'customer.subscription.created': {
          const eventData = event.data.object;
          await WebhookController.handleCreateSubscriptionEvent(
            request,
            eventData,
          );
          break;
        }
        // case 'charge.succeeded': {
        //   const eventData = event.data.object;
        //   await WebhookController.handleChargeSucceeded(request, eventData);
        //   break;
        // }
        default: {
          request.logger.info(
            `WebhookController.stripeWebhook Unhandled event type ${event.type}`,
          );
        }
      }

      return h
        .response({
          statusCode: 200,
          message: 'Stripe webhook executed successfully.',
        })
        .code(200);
    } catch (err) {
      request.logger.error(err, 'WebhookController.stripeWebhook');
      errorHandler(err);
    }
  }

  /**
   * To handle the stripe webhook handle invoice payment succeeded event
   *
   * @param {Hapi request obj} request - request object
   * @param {hapi handler} h - hapi handler object
   */
  // static async handlePaymentIntentSucceededEvent(request, subscription) {
  //   const stripe = Stripe.get();
  //   request.logger.info(
  //     `WebhookController.handlePaymentIntentSucceededEvent called! ${JSON.stringify(
  //       subscription,
  //     )}`,
  //   );
  //   const metadata = get(subscription, 'metadata', '');
  //   const priceId = get(metadata, 'priceId', '');
  //   const stripeCustomerId = get(subscription, 'customer', '');
  //   const paymentIntentId = get(subscription, 'payment_intent', '');
  //   if (paymentIntentId !== '') {
  //     // save payment method
  //     await PaymentsController.saveResponse({
  //       intentId: paymentIntentId,
  //       response: subscription,
  //       status: 'succeed',
  //     });
  //   }

  //   let trialEndTimestamp = 0;
  //   // let days;

  //   if (get(metadata, 'previosSubscription', '') !== '') {
  //     request.logger.info(
  //       `Canceling active subscription: ${get(
  //         metadata,
  //         'previosSubscription',
  //         '',
  //       )}`,
  //     );
  //     const previosSubscription = await stripe.subscriptions.retrieve(
  //       get(metadata, 'previosSubscription', ''),
  //     );
  //     // Calculate the remaining days using moment

  //     const currentPeriodEnd = previosSubscription.current_period_end;

  //     request.logger.info(
  //       `WebhookController.handlePaymentIntentSucceededEvent Customer check previos Subscription active or trialing`,
  //     );
  //     if (
  //       previosSubscription.status === 'active' ||
  //       previosSubscription.status === 'trialing'
  //     ) {
  //       trialEndTimestamp = currentPeriodEnd; // moment().add(remainingSeconds, 'seconds').unix();
  //       request.logger.info(`Calculated trial_end: ${trialEndTimestamp}`);
  //       request.logger.info(
  //         `WebhookController.handlePaymentIntentSucceededEvent canceled previos Subscription ${get(
  //           metadata,
  //           'previosSubscription',
  //           '',
  //         )}`,
  //       );
  //       await stripe.subscriptions.cancel(
  //         get(metadata, 'previosSubscription', ''),
  //       );
  //     }
  //   }
  //   const newMetadata = assign({}, get(subscription, 'metadata', {}), {
  //     remainingDays: trialEndTimestamp,
  //   });
  //   const couponCode = get(metadata, 'couponCode', '');
  //   const subscriptionPayload = {
  //     customer: stripeCustomerId,
  //     items: [
  //       { price: priceId }, // Price ID for the subscription product
  //     ],
  //     metadata: newMetadata,
  //     default_payment_method: subscription.payment_method,
  //     expand: ['latest_invoice.payment_intent'],
  //     ...(couponCode !== '' && {
  //       discounts: [
  //         {
  //           promotion_code: couponCode,
  //         },
  //       ],
  //     }),
  //   };
  //   request.logger.info(
  //     `WebhookController.handlePaymentIntentSucceededEvent subscription payload ${JSON.stringify(
  //       subscriptionPayload,
  //     )}`,
  //   );
  //   // if (metadata.planSlug !== 'trial') {
  //   const subscriptions =
  //     await stripe.subscriptions.create(subscriptionPayload);

  //   const duplicateRequest = Object.assign({}, request);
  //   duplicateRequest.user = {
  //     email: get(newMetadata, 'email', '') || get(newMetadata, 'userEmail', ''),
  //   };
  //   duplicateRequest.payload = {
  //     billing: get(metadata, 'billing', null),
  //     expired: subscriptions.current_period_end,
  //     planId: get(newMetadata, 'planId', ''),
  //     amount: get(subscriptions, 'plan.amount', 0),
  //     customerId: subscriptions.customer,
  //     subscriptionId: subscriptions.id,
  //     type: get(newMetadata, 'planSlug', ''),
  //     cancelAtPeriodEnd: subscriptions.cancel_at_period_end,
  //     status: 'active',
  //   };
  //   request.logger.info(
  //     `WebhookController.handlePaymentIntentSucceededEvent update subscription in IM email ${
  //       duplicateRequest.user.email
  //     } subscription obj ${JSON.stringify(duplicateRequest.payload)}`,
  //   );

  //   await UsersController.updateUserMeta(duplicateRequest);
  //   // }
  //   return subscription;
  // }

  /**
  //  * To handle the stripe webhook
  //  *
  //  * @param {Hapi request obj} request - request object
  //  * @param {hapi handler} h - hapi handler object
  //  */
  // static async handleChargeSucceeded(request, charge) {
  //   const stripe = Stripe.get();
  //   request.logger.info(
  //     `WebhookController.handleChargeSucceeded called! ${JSON.stringify(
  //       charge,
  //     )}`,
  //   );
  //   const stripeCustomerId = get(charge, 'customer', '');
  //   const paymentMethodId = get(charge, 'payment_method', '');
  //   const paymentIntentId = get(charge, 'payment_intent', '');

  //   // save payment method
  //   await PaymentsController.saveResponse({
  //     intentId: paymentIntentId,
  //     response: charge,
  //     status: 'pending',
  //   });
  //   request.logger.info(
  //     `WebhookController.handleChargeSucceeded attach payment method ${paymentMethodId} customer ${stripeCustomerId}}`,
  //   );
  //   await stripe.paymentMethods.attach(paymentMethodId, {
  //     customer: stripeCustomerId,
  //   });

  //   // update customer on stripe
  //   await stripe.customers.update(stripeCustomerId, {
  //     invoice_settings: {
  //       default_payment_method: paymentMethodId,
  //     },
  //   });

  //   return charge;
  // }

  /**
   * To handle the stripe webhook
   *
   * @param {Hapi request obj} request - request object
   * @param {hapi handler} h - hapi handler object
   */
  static async handleCreateSubscriptionEvent(request, subscription) {
    request.logger.info(
      `WebhookController.handleCreateSubscriptionEvent attach payment method ${JSON.stringify(
        subscription,
      )}`,
    );
    const stripe = Stripe.get();

    const metadata = get(subscription, 'metadata', {});
    const expireDate = subscription.current_period_end;
    const customerSubscriptions = await stripe.subscriptions.list({
      customer: subscription.customer,
    });
    const activeSubscription = find(customerSubscriptions.data, (item) =>
      includes(['active', 'trialing'], item.status),
    );

    const duplicateRequest = Object.assign({}, request);
    duplicateRequest.user = {
      email: get(metadata, 'email', '') || get(metadata, 'userEmail', ''),
    };
    duplicateRequest.payload = {
      billing: get(metadata, 'billing', null),
      expired: expireDate,
      planId: get(metadata, 'planId', ''),
      amount: get(subscription, 'plan.amount', 0),
      customerId: subscription.customer,
      subscriptionId: subscription.id,
      type: get(metadata, 'planSlug', ''),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      status: subscription.status === 'trialing' ? 'active' : 'active',
    };
    request.logger.info(
      `WebhookController.handleCreateSubscriptionEvent update subscription in IM email ${
        duplicateRequest.user.email
      } subscription obj ${JSON.stringify(duplicateRequest.payload)}`,
    );

    if (subscription.status === 'incomplete' && isEmpty(activeSubscription)) {
      const getUserRequest = request;
      let email = get(metadata, 'email', '');
      if (email === '') {
        email = get(metadata, 'userEmail', '');
      }
      getUserRequest.user = { email };
      const userDetails = await UsersController.getUserDetails(getUserRequest);
      const userFirstObj = get(userDetails, 'docs[0]', {});
      const slug =
        get(userFirstObj, 'userMeta.isLegacy', false) === true
          ? 'legacy'
          : 'free';
      const slugRequest = request;
      slugRequest.payload = {
        slug,
      };
      const planDetails = await PlanController.getPlanBySlug(slugRequest);
      duplicateRequest.payload.type = get(planDetails, 'slug', '');
      duplicateRequest.payload.status =
        get(userFirstObj, 'userMeta.isLegacy', false) === true
          ? 'active'
          : 'draft';
      duplicateRequest.payload.planId = get(planDetails, '_id', '');
      duplicateRequest.payload.cancelAtPeriodEnd = false;
    }
    if (!isEmpty(activeSubscription)) {
      duplicateRequest.payload = {
        billing: get(metadata, 'billing', null),
        expired: activeSubscription.current_period_end,
        planId: get(activeSubscription, 'metadata.planId', ''),
        amount: get(activeSubscription, 'plan.amount', 0),
        customerId: activeSubscription.customer,
        subscriptionId: activeSubscription.id,
        type: get(activeSubscription, 'metadata.planSlug', ''),
        cancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
        status: subscription.status === 'trialing' ? 'active' : 'active',
      };
    }
    await UsersController.updateUserMeta(duplicateRequest);
    const where = { 'creator.email': duplicateRequest.user.email };
    const data = {
      $set: {
        'creator.metadata': { subscriptionType: duplicateRequest.payload.type },
      },
    };
    await ProjectService.updateOneProject(where, data);

    const subscriptionObj = {
      user: {
        userId: get(metadata, 'userId', ''),
        email: get(metadata, 'email', '') || get(metadata, 'userEmail', ''),
      },
      planId: get(metadata, 'planId', ''),
      customerId: subscription.customer,
      amount: get(subscription, 'plan.amount', 0),
      subscriptionId: subscription.id,
      response: subscription,
      startDate: moment(subscription.current_period_start * 1000),
      endDate: moment(expireDate * 1000),
      renewalDate: moment(subscription.current_period_end * 1000),
      autoRenew: !subscription.cancel_at_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      type: get(metadata, 'planSlug', ''),
      status: subscription.status === 'trialing' ? 'active' : 'active',
    };
    request.logger.info(
      `WebhookController.handleCreateSubscriptionEvent subscription obj ${JSON.stringify(
        subscriptionObj,
      )}`,
    );
    await subscriptionServices.save(subscriptionObj);

    return subscription;
  }

  static async handleSubscriptionUpdatedEvent(request, event) {
    const stripe = Stripe.get();
    const subscription = event.data.object;
    const previousStatus = get(event, 'data.previous_attributes.status', '');

    request.logger.info(
      `WebhookController.handleSubscriptionUpdatedEvent triggered for subscription ${subscription.id}, previous status: ${previousStatus}`,
    );

    // Step 1: Check if we need to void the draft invoice
    if (subscription.status === 'active' && previousStatus === 'trialing') {
      // Step 2: Get all invoices of the customer
      const invoices = await stripe.invoices.list({
        customer: subscription.customer,
        limit: 10, // Optional: adjust as needed
      });

      for (const invoice of invoices.data) {
        if (invoice.status === 'draft') {
          try {
            // Finalize the invoice
            const finalizedInvoice = await stripe.invoices.finalizeInvoice(
              invoice.id,
            );
            request.logger.info(`✅ Finalized draft invoice: ${invoice.id}`);

            // After finalize, it becomes "open"
            if (finalizedInvoice.status === 'open') {
              await stripe.invoices.voidInvoice(invoice.id);
              request.logger.info(`🧨 Voided finalized invoice: ${invoice.id}`);
            }
          } catch (err) {
            request.logger.error(
              `❌ Error handling invoice ${invoice.id}: ${err.message}`,
            );
          }
        }
      }
    }

    // Optional: Add any update to userMeta or DB logging if needed
    request.logger.info(
      `WebhookController.handleSubscriptionUpdatedEvent completed for ${subscription.id}`,
    );
  }

  /**
   * To handle the stripe webhook
   *
   * @param {Hapi request obj} request - request object
   * @param {hapi handler} h - hapi handler object
   */
  static async updateSubscription(
    request,
    subscription,
    previousAttributes = {},
  ) {
    request.logger.info(
      `WebhookController.updateSubscription attach payment method ${JSON.stringify(
        subscription,
      )}`,
    );
    const stripe = Stripe.get();
    const duplicateRequest = Object.assign({}, request);

    const metadata = get(subscription, 'metadata', {});
    const customerSubscriptions = await stripe.subscriptions.list({
      customer: subscription.customer,
    });

    const activeSubscription = find(customerSubscriptions.data, (item) =>
      includes(['active', 'trialing'], item.status),
    );

    // ✅ VOID INVOICE IF TRIAL → ACTIVE
    if (
      subscription.status === 'active' &&
      get(previousAttributes, 'status') === 'trialing'
    ) {
      const invoices = await stripe.invoices.list({
        customer: subscription.customer,
        limit: 10,
      });

      for (const invoice of invoices.data) {
        if (invoice.status === 'draft') {
          try {
            const finalizedInvoice = await stripe.invoices.finalizeInvoice(
              invoice.id,
            );
            request.logger.info(`✅ Finalized draft invoice: ${invoice.id}`);

            if (finalizedInvoice.status === 'open') {
              await stripe.invoices.voidInvoice(invoice.id);
              request.logger.info(`🧨 Voided finalized invoice: ${invoice.id}`);
            }
          } catch (err) {
            request.logger.error(
              `❌ Error finalizing/voiding invoice ${invoice.id}: ${err.message}`,
            );
          }
        }
      }
    }

    duplicateRequest.payload = {
      billing: get(metadata, 'billing', null),
      type: metadata.planSlug,
      planId: metadata.planId,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      status: 'active',
      amount: get(subscription, 'items.data[0].price.unit_amount', 0),
      amountPaid: get(subscription, 'items.data[0].price.unit_amount', 0),
    };

    // ✅ Handle trial upgrade if remainingDays exist
    if (
      get(metadata, 'remainingDays', 0) > 0 &&
      subscription.status !== 'incomplete'
    ) {
      const now = moment();
      const remainingDaysUnix = get(metadata, 'remainingDays', 0);

      if (remainingDaysUnix) {
        const remainingTime = moment.unix(remainingDaysUnix);
        const totalHours = remainingTime.diff(now, 'hours');
        const days = Math.floor(totalHours / 24);
        const hours = totalHours % 24;

        const daays = moment.unix(remainingDaysUnix).diff(moment(), 'days');

        if (subscription.status !== 'incomplete_expired') {
          const trialSubscription = find(customerSubscriptions.data, (item) =>
            includes(['trialing'], item.status),
          );
          await stripe.subscriptions.cancel(trialSubscription.id);
        }

        const expireDate = moment
          .unix(subscription.current_period_end)
          .add(days, 'days')
          .add(hours, 'hours')
          .unix();

        duplicateRequest.payload.expired = expireDate;

        const newMetadata = assign({}, metadata, {
          remainingDays: 0,
          description: `${daays} days added to the subscription trial period for the new plan upgrade.`,
        });

        // const fiveMinutesLater = Math.floor(Date.now() / 1000) + 120;
        await stripe.subscriptions.update(subscription.id, {
          // trial_end: fiveMinutesLater,
          trial_end: get(metadata, 'remainingDays', 0), // Update
          metadata: newMetadata,
          proration_behavior: 'none',
        });
      }
    }

    const getUserRequest = request;
    getUserRequest.user = {
      email: get(metadata, 'email') || get(metadata, 'userEmail'),
    };
    const userDetails = await UsersController.getUserDetails(getUserRequest);
    const userFirstObj = get(userDetails, 'docs[0]', {});
    const slug =
      get(userFirstObj, 'userMeta.isLegacy', false) === true
        ? 'legacy'
        : 'free';

    const slugRequest = request;
    slugRequest.payload = { slug };

    const planDetails = await PlanController.getPlanBySlug(slugRequest);

    duplicateRequest.user = {
      email: get(metadata, 'email', '') || get(metadata, 'userEmail', ''),
    };

    if (
      subscription.status !== 'active' &&
      subscription.status !== 'trialing' &&
      isEmpty(activeSubscription)
    ) {
      duplicateRequest.payload.type = planDetails.slug;
      duplicateRequest.payload.planId = planDetails._id;
      duplicateRequest.payload.status = slug === 'free' ? 'draft' : 'active';
      duplicateRequest.payload.cancelAtPeriodEnd = false;
    } else {
      duplicateRequest.payload.type = activeSubscription.metadata.planSlug;
      duplicateRequest.payload.planId = activeSubscription.metadata.planId;
      duplicateRequest.payload.cancelAtPeriodEnd =
        activeSubscription.cancel_at_period_end;
      duplicateRequest.payload.amount = get(
        activeSubscription,
        'items.data[0].price.unit_amount',
        0,
      );
      duplicateRequest.payload.amountPaid = get(
        activeSubscription,
        'items.data[0].price.unit_amount',
        0,
      );
    }

    // 🔁 Handle renewal
    const currentPeriodStart = subscription.current_period_start;
    const userMetaExpiredDate = get(userFirstObj, 'userMeta.expired');

    if (
      currentPeriodStart > userMetaExpiredDate &&
      subscription.status === 'active'
    ) {
      request.logger.info(
        `WebhookController.updateSubscription detected renewal for user ${duplicateRequest.user.email}`,
      );
      duplicateRequest.payload.expired = subscription.current_period_end;
      duplicateRequest.payload.amountPaid = get(
        subscription,
        'items.data[0].price.unit_amount',
        0,
      );
    }

    // 🏷️ Handle Discount
    if (subscription.discount !== null) {
      const discountData = subscription.discount;
      const latestInvoiceId = subscription.latest_invoice;
      const latestInvoice = await stripe.invoices.retrieve(latestInvoiceId);

      if (latestInvoice.status === 'paid') {
        duplicateRequest.payload.discount = {
          couponName: get(discountData, 'coupon.name', ''),
          amount_off: get(discountData, 'coupon.amount_off', null),
          percent_off: get(discountData, 'coupon.percent_off', null),
        };
        duplicateRequest.payload.amountPaid = latestInvoice.amount_paid;
      }
    }

    await UsersController.updateUserMeta(duplicateRequest);

    const where = { 'creator.email': duplicateRequest.user.email };
    const data = {
      $set: {
        'creator.metadata': {
          subscriptionType: duplicateRequest.payload.type,
        },
      },
    };
    await ProjectService.updateOneProject(where, data);

    const subscriptionObj = {
      response: subscription,
      renewalDate: null,
      autoRenew: !subscription.cancel_at_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: moment(subscription.canceled_at * 1000),
      status: subscription.status,
    };

    if (
      (subscription.status === 'incomplete_expired' ||
        subscription.status === 'incomplete') &&
      isEmpty(activeSubscription)
    ) {
      subscriptionObj.type = planDetails.slug;
      subscriptionObj.planId = planDetails._id;
      subscriptionObj.status = slug === 'free' ? 'draft' : 'active';
      duplicateRequest.payload.cancelAtPeriodEnd = false;
    }

    await subscriptionServices.update(
      { subscriptionId: subscription.id },
      subscriptionObj,
    );

    try {
      const userEmail = get(duplicateRequest, 'user.email', '');
      const planType = get(duplicateRequest, 'payload.type', 'free');
      const userId = get(userFirstObj, '_id', null);
      const billingCycle = get(metadata, 'billing.cycle', 'monthly');
      const subscriptionStatus = get(subscription, 'status', '');

      let eventName = 'subscription_updated';
      const eventData = {
        plan_type: planType,
        billing_cycle: billingCycle,
      };

      const now = Date.now();
      const nowUnix = Math.floor(now / 1000);

      if (planType === 'pro' || planType === 'enterprise') {
        eventName = 'paid_subscription_started';
      } else if (planType === 'free') {
        eventName = 'free_subscription_started';
        eventData.switched_at = nowUnix;
      } else if (planType === 'legacy') {
        eventName = 'legacy_subscription_started';
        eventData.switched_at = nowUnix;
      } else if (subscriptionStatus === 'trialing') {
        eventName = 'trial_subscription_started';

        const trialStart = get(subscription, 'start_date', nowUnix);
        const trialEnd = get(subscription, 'trial_end', nowUnix + 7 * 86400);

        eventData.start_time = trialStart;
        eventData.expire_time = trialEnd;
      }

      if (userId) {
        const key = `${userId}_${eventName}`;

        if (!global.lastEventSent) global.lastEventSent = {};

        if (
          global.lastEventSent[key] &&
          now - global.lastEventSent[key] < 10000
        ) {
          request.logger.info(`⚠️ Duplicate event '${eventName}' for ${userEmail} suppressed`);
        } else {
          global.lastEventSent[key] = now;

          await CRMService.trackEvent(userId, userEmail, eventName, eventData);

          request.logger.info(`📬 Sent ${eventName} to Customer.io for user: ${userEmail}`);
        }
      } else {
        request.logger.warn(`⚠️ No userId found to send event to Customer.io for email: ${userEmail}`);
      }
    } catch (err) {
      request.logger.error(`❌ Failed to send event to Customer.io: ${err.message}`);
    }
    return subscription;
  }

  /**
   * To handle the customer subscription deleted webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  static async handleSubscriptionDeleted(request, subscription) {
    request.logger.info(
      'StripePaymentsService.handleSubscriptionDeleted called!',
    );
    const stripe = Stripe.get();

    const metadata = get(subscription, 'metadata', {});
    const customerSubscriptions = await stripe.subscriptions.list({
      customer: subscription.customer,
    });
    const activeSubscription = find(customerSubscriptions.data, (item) =>
      includes(['active', 'trialing'], item.status),
    );
    if (!isEmpty(activeSubscription)) {
      return subscription;
    }
    const subscriptionObj = {
      response: subscription,
      renewalDate: null,
      autoRenew: !subscription.cancel_at_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: moment(subscription.canceled_at * 1000),
    };
    const getUserRequest = request;
    let email = get(metadata, 'email', '');
    if (email === '') {
      email = get(metadata, 'userEmail', '');
    }
    getUserRequest.user = { email };
    const userDetails = await UsersController.getUserDetails(getUserRequest);
    const userFirstObj = get(userDetails, 'docs[0]', {});
    const slug =
      get(userFirstObj, 'userMeta.isLegacy', false) === true
        ? 'legacy'
        : 'free';
    const slugRequest = request;
    slugRequest.payload = {
      slug,
    };
    const planDetails = await PlanController.getPlanBySlug(slugRequest);
    const getActiveSubscripotion = await stripe.subscriptions.list({
      customer: get(subscription, 'customer'), // Customer ID
      limit: 1, // Get only the most recent subscription
      status: 'active', // Filter for active subscriptions
    });

    const activeSubscriptionMetadata = get(
      getActiveSubscripotion,
      'data[0].metadata',
      {},
    );

    if (
      subscription.status === 'canceled' &&
      get(activeSubscriptionMetadata, 'planSlug', '') !== 'enterprise'
    ) {
      subscriptionObj.type = get(planDetails, 'slug', '');
      subscriptionObj.status =
        get(userFirstObj, 'userMeta.isLegacy', false) === true
          ? 'active'
          : 'draft';
      subscriptionObj.planId = get(planDetails, '_id', '');
    } else {
      subscriptionObj.status = 'active';
    }
    request.logger.info(
      `WebhookController.handleSubscriptionDeleted subscription obj ${JSON.stringify(
        subscriptionObj,
      )}`,
    );
    await subscriptionServices.update(
      { subscriptionId: subscription.id },
      subscriptionObj,
    );
    const duplicateRequest = Object.assign({}, request);
    duplicateRequest.user = { email };
    duplicateRequest.payload = {
      billing: get(metadata, 'billing', null),
      expired: subscription.current_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
    };
    if (
      subscription.status === 'canceled' &&
      get(activeSubscriptionMetadata, 'planSlug', '') !== 'enterprise'
    ) {
      duplicateRequest.payload.type = get(planDetails, 'slug', '');
      duplicateRequest.payload.status =
        get(userFirstObj, 'userMeta.isLegacy', false) === true
          ? 'active'
          : 'draft';
      duplicateRequest.payload.planId = get(planDetails, '_id', '');
      duplicateRequest.payload.cancelAtPeriodEnd = false;
    } else {
      duplicateRequest.payload.status = 'active';
    }

    request.logger.info(
      `WebhookController.handleSubscriptionDeleted update subscription in IM email ${
        duplicateRequest.user.email
      } subscription obj ${JSON.stringify(duplicateRequest.payload)}`,
    );
    await UsersController.updateUserMeta(duplicateRequest);
    const where = { 'creator.email': duplicateRequest.user.email };
    const data = {
      $set: {
        'creator.metadata': { subscriptionType: duplicateRequest.payload.type },
      },
    };
    await ProjectService.updateOneProject(where, data);
    return subscription;
  }

  /**
   * To handle the checkout session event
   *
   * @param {object} eventData - event data
   * @returns {Promise<void>} - response object
   */
  static async handleCheckoutSession(request, eventData) {
    request.logger.info('StripePaymentsService.handleCheckoutSession called!');
    const subscription = eventData.subscription;
    // Access the metadata
    const stripe = Stripe.get();
    const subscriptionData = await stripe.subscriptions.retrieve(subscription);
    const metadata = subscriptionData.metadata;
    if (get(metadata, 'previosSubscription', '')) {
      const previosSubscription = await stripe.subscriptions.retrieve(
        get(metadata, 'previosSubscription', ''),
      );
      if (
        previosSubscription.status === 'active' ||
        previosSubscription.status === 'trialing'
      ) {
        request.logger.info(
          `StripePaymentsService.handleCheckoutSession canceled previos subscription ${get(
            metadata,
            'previosSubscription',
            '',
          )}`,
        );

        await stripe.subscriptions.cancel(
          get(metadata, 'previosSubscription', ''),
        );
      }
    }
    let expireDate;
    if (get(metadata, 'remainingDays', 0) > 0) {
      const remainingDaysUnix = get(metadata, 'remainingDays', 0);

      if (remainingDaysUnix) {
        const now = moment();
        const remainingTime = moment.unix(remainingDaysUnix);

        const totalHours = remainingTime.diff(now, 'hours'); // Total hours remaining
        const days = Math.floor(totalHours / 24); // Extract days
        const hours = totalHours % 24; // Extract remaining hours

        const daays = moment
          .unix(get(metadata, 'remainingDays', 0))
          .diff(moment(), 'days');

        expireDate = moment
          .unix(subscriptionData.current_period_end) // Convert the timestamp to a moment object
          .add(days, 'days') // Add days
          .add(hours, 'hours') // Add hours
          .unix();

        const newMetadata = assign({}, metadata, {
          remainingDays: 0,
          description: `${daays} days added to the subscription trial period for the new plan upgrade.`,
        });

        await stripe.subscriptions.update(subscription, {
          trial_end: get(metadata, 'remainingDays', 0), // Update trial_end
          metadata: newMetadata,
          proration_behavior: 'none',
        });
      }
    } else {
      expireDate = eventData.current_period_end;
    }
    const duplicateRequest = Object.assign({}, request);

    duplicateRequest.user = {
      email: get(metadata, 'email', '') || get(metadata, 'userEmail', ''),
    };
    duplicateRequest.payload = {
      billing: get(metadata, 'billing', null),
      expired: expireDate,
      planId: get(metadata, 'planId', ''),
      amount: get(subscriptionData, 'plan.amount', 0),
      customerId: eventData.customer,
      subscriptionId: subscription,
      type: get(metadata, 'planSlug', ''),
      cancelAtPeriodEnd: subscriptionData.cancel_at_period_end,
      status: 'active',
    };
    request.logger.info(
      `WebhookController.handleCheckoutSession update subscription in IM email ${
        duplicateRequest.user.email
      } subscription obj ${JSON.stringify(duplicateRequest.payload)}`,
    );

    await UsersController.updateUserMeta(duplicateRequest);
    const where = { 'creator.email': duplicateRequest.user.email };
    const data = {
      $set: {
        'creator.metadata': { subscriptionType: duplicateRequest.payload.type },
      },
    };
    await ProjectService.updateOneProject(where, data);

    try {
      const subscriptionObj = {
        user: {
          userId: get(metadata, 'userId', ''),
          email: get(metadata, 'email', '') || get(metadata, 'userEmail', ''),
        },
        planId: get(metadata, 'planId', ''),
        customerId: subscriptionData.customer,
        amount: get(subscriptionData, 'plan.amount', 0),
        subscriptionId: subscriptionData.id,
        response: eventData,
        startDate: moment(subscriptionData.current_period_start * 1000),
        endDate: moment(expireDate * 1000),
        renewalDate: moment(expireDate * 1000),
        autoRenew: !subscriptionData.cancel_at_period_end,
        cancelAtPeriodEnd: subscriptionData.cancel_at_period_end,
        type: get(metadata, 'planSlug', ''),
        status: 'active',
      };
      request.logger.info(
        `WebhookController.handleCheckoutSession subscription obj ${JSON.stringify(
          subscriptionObj,
        )}`,
      );
      await subscriptionServices.save(subscriptionObj);
    } catch (error) {
      global.logger().error(error, `Error in BaseService.save`);
    }
    request.logger.info(
      `handleCheckoutSession:handleCheckoutSession ${subscription.id} settled successfully.`,
    );
    return eventData;
  }
}

module.exports = WebhookController;
