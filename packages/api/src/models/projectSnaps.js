const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate');
const { Schema } = mongoose;

const ProjectSnapsSchema = new Schema(
  {
    projectId: { type: Schema.Types.ObjectId, required: true, ref: 'Project' },
    publish: { type: Boolean },
    creator: {
      userId: { type: Schema.Types.ObjectId, required: true },
      email: { type: String },
      fullName: { type: String },
      profileImage: { type: String },
    },
    body: { type: String },
    hash: { type: String },
    notes: { type: String },
    activities: [
      {
        action: {
          type: String,
          enum: ['letsTalk', 'tracking', 'notInterested', 'view'],
        },
        user: {
          userId: { type: Schema.ObjectId, required: true },
          name: { type: String },
          email: { type: String },
          profileImage: { type: String },
        },
        createdAt: { type: Date, default: Date.now },
      },
    ],
    deleted: { type: Boolean, default: false },
  },
  { timestamps: true },
);
ProjectSnapsSchema.plugin(mongoosePaginate);
const ProjectSnaps = mongoose.model('ProjectSnaps', ProjectSnapsSchema);
module.exports = ProjectSnaps;
