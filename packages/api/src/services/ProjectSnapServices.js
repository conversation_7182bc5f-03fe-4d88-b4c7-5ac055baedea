const ProjectSnap = require('../models/projectSnaps');
const { errorHandler } = require('../utils/errorHandler');

/**
 * @class Project Services
 */

class ProjectSnapServices {
  async fetch(where, options) {
    try {
      return await ProjectSnap.paginate(where, options);
    } catch (err) {
      return errorHandler(err);
    }
  }

  async update(where, query) {
    try {
      return await ProjectSnap.updateOne(where, query, {
        new: true,
      });
    } catch (err) {
      return errorHandler(err);
    }
  }

  async get(where) {
    try {
      return await ProjectSnap.findOne(where).populate('projectId');
    } catch (err) {
      return errorHandler(err);
    }
  }

  async find(where) {
    try {
      // Use aggregation to sort activities within each document
      const result = await ProjectSnap.aggregate([
        { $match: where },
        {
          $addFields: {
            activities: {
              $sortArray: {
                input: '$activities',
                sortBy: { createdAt: -1 },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'projects',
            localField: 'projectId',
            foreignField: '_id',
            as: 'projectId',
          },
        },
        {
          $unwind: {
            path: '$projectId',
            preserveNullAndEmptyArrays: true,
          },
        },
      ]);
      return result;
    } catch (err) {
      return errorHandler(err);
    }
  }
}

module.exports = ProjectSnapServices;
