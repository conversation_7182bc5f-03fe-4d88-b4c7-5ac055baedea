const ProjectSnap = require('../models/projectSnaps');
const { errorHandler } = require('../utils/errorHandler');

/**
 * @class Project Services
 */

class ProjectSnapServices {
  async fetch(where, options) {
    try {
      return await ProjectSnap.paginate(where, options);
    } catch (err) {
      return errorHandler(err);
    }
  }

  async update(where, query) {
    try {
      return await ProjectSnap.updateOne(where, query, {
        new: true,
      });
    } catch (err) {
      return errorHandler(err);
    }
  }

  async get(where) {
    try {
      return await ProjectSnap.findOne(where).populate('projectId');
    } catch (err) {
      return errorHandler(err);
    }
  }

  async find(where) {
    try {
      const result = await ProjectSnap.find(where).populate('projectId').lean();

      // Sort activities within each document manually
      result.forEach((doc) => {
        if (doc.activities && doc.activities.length > 0) {
          console.log(
            'Before sorting activities:',
            doc.activities.map((a) => ({
              action: a.action,
              createdAt: a.createdAt,
            })),
          );
          doc.activities.sort((a, b) => {
            const dateA = new Date(a.createdAt || 0);
            const dateB = new Date(b.createdAt || 0);
            return dateB - dateA; // Latest first
          });
          console.log(
            'After sorting activities:',
            doc.activities.map((a) => ({
              action: a.action,
              createdAt: a.createdAt,
            })),
          );
        }
      });

      return result;
    } catch (err) {
      return errorHandler(err);
    }
  }
}

module.exports = ProjectSnapServices;
