import React from 'react';
import PropTypes from 'prop-types';
import Router from 'next/router';
import PageHeaderSection from 'sharedComponents/PageHeaderSection/pageHeaderSection';
import CarouselSection from './CarouselSection';
import MySubmissions from './MySubmissions';
import InterestedProjects from './InterestedProjects';
import MyProjects from './MyProject';

/**
 * DashboardContent - A component to handle the main dashboard content layout
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Rendered component
 */
const DashboardContent = ({
  isLoading,
  myCallOutList,
  callOutList,
  withButton = false,
  welcomeTitle = 'Dashboard',
  // Project-related props
  projectsList = [],
  deleteProject,
  collaboratorList = [],
  createCollaborator,
  fetchCollaboratorList,
  updateOtherDocuments,
  sendReminderToCollaborator,
  fetchCollaborator,
  deleteCollaborator,
  userData = {},
  removeSectionItems,
  softDeleteProject,
}) => {
  if (isLoading) {
    return (
      <div className="text-center py-4">
        <p>Loading...</p>
      </div>
    );
  }

  // Filter the callout lists to get only published items
  // const publishedMyCallouts = myCallOutList.filter(
  //   (callout) => callout.isPublished,
  // );

  const discoverCallouts = callOutList.filter(
    (callout) =>
      // Only show published callouts
      callout.isPublished &&
      // Filter out callouts that are in myCallOutList (by comparing IDs)
      !myCallOutList.some((myCallout) => myCallout._id === callout._id),
  );

  return (
    <>
      <PageHeaderSection
        title={welcomeTitle}
        showButton={withButton}
        buttonLabel={withButton ? 'View All Callouts' : undefined}
        onButtonClick={withButton ? () => Router.push('/callouts') : undefined}
        titleClass="col-12"
      />
      {welcomeTitle === 'welcome to Smash' && (
        <p className="text-center text-primary mb-0 mt-3 p2">
          Your Smash Dashboard
        </p>
      )}

      {/* My Projects Component - Show user's projects */}
      <MyProjects
        projectsList={projectsList}
        deleteProject={deleteProject}
        collaboratorList={collaboratorList}
        createCollaborator={createCollaborator}
        fetchCollaboratorList={fetchCollaboratorList}
        updateOtherDocuments={updateOtherDocuments}
        sendReminderToCollaborator={sendReminderToCollaborator}
        fetchCollaborator={fetchCollaborator}
        deleteCollaborator={deleteCollaborator}
        userData={userData}
        removeSectionItems={removeSectionItems}
        softDeleteProject={softDeleteProject}
      />

      {/* Submissions Component - Will only render if there are submissions */}
      <MySubmissions />

      {/* My Callouts with Carousel - Only show if there are published callouts owned by the user */}
      {myCallOutList.length > 0 && (
        <CarouselSection
          title="My Callouts"
          items={myCallOutList}
          type="my"
          maxItems={9}
          slidesPerPage={3}
          showViewMore={true}
          viewMoreUrl="/callouts?scrollTo=myCallouts"
        />
      )}

      {/* Discover Callouts with Carousel - Only show if there are published callouts to discover */}
      {discoverCallouts.length > 0 && (
        <CarouselSection
          title="Discover call outs"
          items={discoverCallouts}
          type="discover"
          maxItems={9}
          slidesPerPage={3}
          showViewMore={true}
          viewMoreUrl="/callouts?scrollTo=discoverCallouts"
        />
      )}

      {/* Projects I'm Interested In section - Will only render if there are interested projects */}
      <InterestedProjects />
    </>
  );
};

DashboardContent.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  myCallOutList: PropTypes.array.isRequired,
  callOutList: PropTypes.array.isRequired,
  withButton: PropTypes.bool,
  welcomeTitle: PropTypes.string,
  // Project-related props
  projectsList: PropTypes.array,
  deleteProject: PropTypes.func,
  collaboratorList: PropTypes.array,
  createCollaborator: PropTypes.func,
  fetchCollaboratorList: PropTypes.func,
  updateOtherDocuments: PropTypes.func,
  sendReminderToCollaborator: PropTypes.func,
  fetchCollaborator: PropTypes.func,
  deleteCollaborator: PropTypes.func,
  userData: PropTypes.object,
  removeSectionItems: PropTypes.func,
  softDeleteProject: PropTypes.func,
};

export default DashboardContent;
