/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { get, map, filter } from 'lodash';
import * as moment from 'moment';
import {
  setEditSection,
  setTheme,
  toggleTheme,
  updateProjectData,
  setIsClickedOnMenu,
  updateProjectMeta,
} from 'reducer/project';
import { logout } from 'reducer/auth';
import Modal from './notificationModal';
import Style from '../styles/leftSideBar.module.scss';
import Styles from '../styles/notification.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';

// Project Creation form component
class LeftSideBar extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      notificationStatus: false,
    };
  }

  linkButtonHandler = () => {
    const { projectPreviewData } = this.props;
    Router.push(
      '/project/share/[shareHistory]',
      `/project/share/${projectPreviewData._id}`,
    );
  };

  // Calculate the total notification count
  getNotificationCount = () => {
    const { notificationsList, userData } = this.props;
    if (!notificationsList || !userData) return 0;

    let totalCount = 0;
    notificationsList.forEach((item) => {
      if (item.activities) {
        // Count activities from other users (not the current user)
        const otherUserActivities = item.activities.filter(
          (activity) => activity.user.email !== userData.email,
        );
        totalCount += otherUserActivities.length;
      }
    });

    return totalCount;
  };

  // Show hide notification modal
  toggleNotification = () => {
    const { notificationStatus } = this.state;
    const { updateProjectMeta, projectPreviewData } = this.props;
    updateProjectMeta({ isNewNotification: false }, projectPreviewData._id);
    this.setState({ notificationStatus: !notificationStatus });
  };

  // Calculate day and hours.
  diffDaysHours = (date) => {
    const showTime = {};

    // Handle invalid or missing dates
    if (!date) {
      showTime.days = '0 days ago';
      showTime.hours = '0 hours ago';
      showTime.numberOfDays = 0;
      return showTime;
    }

    const now = moment();
    const createdMoment = moment(date);

    // Calculate difference in days
    const diffDate = now.diff(createdMoment, 'days');

    // Calculate difference in hours for same day
    const diffHours = now.diff(createdMoment, 'hours');
    const diffMinutes = now.diff(createdMoment, 'minutes');

    if (diffDate > 1) {
      showTime.days = `${diffDate} days ago`;
    } else if (diffDate === 1) {
      showTime.days = `1 day ago`;
    } else {
      showTime.days = `0 days ago`;
    }

    // For hours calculation, use absolute difference
    if (diffHours > 1) {
      showTime.hours = `${diffHours} hours ago`;
    } else if (diffHours === 1) {
      showTime.hours = `1 hour ago`;
    } else if (diffMinutes > 1) {
      showTime.hours = `${diffMinutes} minutes ago`;
    } else {
      showTime.hours = `Just now`;
    }

    showTime.numberOfDays = diffDate;

    return showTime;
  };

  // This method is used for notification modal body.
  body = () => {
    const { notificationsList, userData } = this.props;
    const notificationArray = map(notificationsList, (item) => {
      // Filter activities to exclude the current user's own actions
      const filteredActivities = filter(item.activities, (snapData) => {
        return snapData.user.email !== userData.email;
      });

      return {
        ...item,
        activities: filteredActivities,
      };
    }).filter((item) => item.activities && item.activities.length > 0);
    return (
      <div className="mt-5 mt-sm-5 mt-md-2">
        {!notificationArray || notificationArray.length === 0 ? (
          <div className="text-left">
            <p className="text-primary p2 pt-3">
              This is where you’ll find feedback to your project when it’s
              shared with decision makers.
            </p>
          </div>
        ) : (
          notificationArray.map((item, index) => (
            <div key={index} className="col-12 border-bottom text-left mb-3">
              {/* Show ALL activities for this snapshot */}
              {item.activities.map((activity, activityIndex) => (
                <div key={activityIndex} className="d-flex flex-column p-3">
                  <div className="d-flex align-items-start">
                    {/* Profile Image */}
                    <div className="col-3 rounded-circle pl-0">
                      <img
                        src={get(
                          activity,
                          'user.profileImage',
                          '/assets/jpg/Placeholder_Avatar_320.jpg',
                        )}
                        height="50px"
                        width="50px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer', objectFit: 'cover' }}
                      />
                    </div>

                    {/* Text Content */}
                    <div className="col-9 p-0">
                      {activity.action === 'letsTalk' && (
                        <p className="p2" style={{ color: '#05012D' }}>
                          <strong>{get(activity, 'user.name')}</strong> wants to
                          talk about this project. You can contact them via
                          email.{' '}
                          <a
                            href={`mailto:${get(activity, 'user.email')}`}
                            className=""
                            style={{ color: '#1743d7' }}
                          >
                            email
                          </a>
                          .
                        </p>
                      )}
                      {activity.action === 'tracking' && (
                        <p className="p2" style={{ color: '#05012D' }}>
                          <strong>{get(activity, 'user.name')}</strong> is
                          tracking this project.
                        </p>
                      )}
                      {activity.action === 'notInterested' && (
                        <p className="p2" style={{ color: '#05012D' }}>
                          <strong>{get(activity, 'user.name')}</strong> is not
                          interested in this project at the moment.
                        </p>
                      )}

                      {/* Status Dot & Timestamp */}
                      <div className="d-flex align-items-center mt-2">
                        <div
                          style={{
                            backgroundColor:
                              activity.action === 'letsTalk'
                                ? '#00E5D5'
                                : activity.action === 'tracking'
                                  ? '#EBFF29'
                                  : '#FF303D',
                            width: '12px',
                            height: '12px',
                          }}
                          className="rounded-circle"
                        />
                        <div className="ml-2">
                          <p className="p3" style={{ color: '#858585' }}>
                            {this.diffDaysHours(activity.createdAt)
                              .numberOfDays !== 0
                              ? this.diffDaysHours(activity.createdAt).days
                              : this.diffDaysHours(activity.createdAt).hours}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))
        )}
      </div>
    );
  };

  render() {
    const { projectPreviewData, isNewNotification } = this.props;
    const { notificationStatus } = this.state;

    return (
      <div>
        <div className="row">
          <div className="col-8">
            {get(projectPreviewData, 'cover.title') && (
              <h2 className={`${Style.projectsTitle}`} data-cy="headerId">
                {projectPreviewData.cover.title}
              </h2>
            )}
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/Bell.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.toggleNotification()}
              />
              {isNewNotification && (
                <div
                  className={`${Styles.notificationCount} carousel-caption text-light rounded-circle p4`}
                  onClick={() => this.toggleNotification()}
                >
                  {this.getNotificationCount()}
                </div>
              )}
            </div>
            <Modal
              modalShow={notificationStatus}
              title="Feedback"
              body={this.body()}
              closeCallback={this.toggleNotification}
              isShowCrossBtn
            />
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/shareIcon.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.linkButtonHandler()}
              />
            </div>
          </div>
        </div>
        <p className={`${Style.registrationText} p2 mt-1`} data-cy="regNo">
          SMASH REGISTRATION #
          {get(projectPreviewData, 'regNo') && projectPreviewData.regNo}
        </p>
      </div>
    );
  }
}

LeftSideBar.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  cover: PropTypes.object.isRequired,
  notificationsList: PropTypes.array.isRequired,
  updateProjectMeta: PropTypes.func.isRequired,
  isNewNotification: PropTypes.number.isRequired,
};

const mapStateToProps = (state) => ({
  editSection: state.project.editSection,
  userData: state.auth.userData,
  isClickedOnMenu: state.project.isClickedOnMenu,
  isNewNotification: state.project.isNewNotification,
  notificationsList: state.project.notificationsList,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setEditSection: (payload) => dispatch(setEditSection(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    updateProjectData: (value, id) => dispatch(updateProjectData(value, id)),
    setTheme: (payload) => dispatch(setTheme(payload)),
    logout: (payload) => dispatch(logout(payload)),
    setIsClickedOnMenu: (payload) => dispatch(setIsClickedOnMenu(payload)),
    updateProjectMeta: (payload, id) =>
      dispatch(updateProjectMeta(payload, id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation('common')(LeftSideBar));
