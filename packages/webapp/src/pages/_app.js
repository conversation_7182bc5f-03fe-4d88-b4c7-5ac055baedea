'use client';

import 'react-dropdown/style.css';
import 'react-toastify/dist/ReactToastify.css';
import '../styles/_overwrite.scss';
import '../styles/global.scss';
import '../styles/mobileFix.css';
import '../sharedComponents/CascadeTemplate/Styles/style.css';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-multi-carousel/lib/styles.css';
import 'react-circular-progressbar/dist/styles.css';
import '@uppy/core/dist/style.css';
import '@uppy/dashboard/dist/style.css';
import '@uppy/image-editor/dist/style.min.css';
import { getSubscription, getPlans } from 'reducer/subscription';

import { useEffect } from 'react';
import { PersistGate } from 'redux-persist/integration/react';
import { ToastContainer } from 'react-toastify';
import { useStore, Provider } from 'react-redux';
import { appWithTranslation } from 'next-i18next';
import Crisp from 'lib/crispChatManager';
import LogRocket from 'lib/logRocketManager';
import GTM4Analytics from 'lib/gtmManager';
import authMiddleware from 'lib/auth';
import { get, includes } from 'lodash';
import { useRouter } from 'next/navigation';
import Socket from 'lib/socket';
import FacebookPixel from 'sharedComponents/facebook-pixel';
import { wrapper } from '../store';
import ErrorBoundary from 'sharedComponents/ErrorBoundary';
import { trackPageView } from 'utils/fbPixel';
import { useDispatch } from 'react-redux';
import SubscriptionJourney from 'sharedComponents/SubscriptionJourney/subscriptionJourney';
import { LayoutProvider } from '../contexts/LayoutContext';

function App({ Component, pageProps }) {
  const store = useStore();
  const router = useRouter();
  const dispatch = useDispatch();

  const { pathname } = router;

  const { token, userData } = store.getState().auth;

  /* Add middleware for authentication */
  const AuthenticatedComponent = authMiddleware(Component, 'spritz');

  useEffect(() => {
    const excludeChatPath = [
      '/callouts/public',
      '/callouts/public/[id]',
      '/callouts/public/[id]/[referrer]',
    ];
    if (!includes(excludeChatPath, get(router, 'pathname'))) {
      // Crisp chat.
      Crisp();
    }

    // Log rocket
    LogRocket();

    // Google analytics
    const gtm4 = new GTM4Analytics(process.env.GtmKey);
    window.gtm4 = gtm4;
    window.gtm4.pageView({
      url: window.location.href + window.location.search,
      title: document.title,
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const initializeSocketAndSubscription = async () => {
      if (token && userData) {
        Socket.init(token);
        trackPageView(userData);
      }
      dispatch(getSubscription(pathname));
      dispatch(getPlans());
    };

    initializeSocketAndSubscription();
  }, [token, userData, dispatch, pathname]);

  return (
    <Provider store={store}>
      <PersistGate persistor={store.__persistor} loading={null}>
        <LayoutProvider>
          <ToastContainer
            limit={1}
            theme="colored"
            className="toast-custom-style"
            position="top-right"
            hideProgressBar
          />
          <FacebookPixel />
          <SubscriptionJourney />
          <ErrorBoundary>
            <AuthenticatedComponent {...pageProps} />
          </ErrorBoundary>
        </LayoutProvider>
      </PersistGate>
    </Provider>
  );
}
export default wrapper.withRedux(appWithTranslation(App));
