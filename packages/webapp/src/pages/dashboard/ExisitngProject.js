/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { reduxForm } from 'redux-form';
import { get, filter } from 'lodash';
import Icon from 'sharedComponents/Icon/Icon';
import Modal from 'sharedComponents/Modal/modal';
import EditIconSvg from 'svgpath/HorizontalThreeDotSvgPath';
import CrossSvg from 'svgpath/CrossIconSvgPath';
import { withTranslation } from 'react-i18next';
import LockIcon from 'sharedComponents/Icon/LockIcon';
import Style from './styles/startApp.module.scss';
import style from './styles/collaborator.module.scss';
import Collaborators from './Collaborator';

class ExisitingProject extends PureComponent {
  constructor() {
    super();
    this.state = {
      showEdit: false,
      showModal: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
      collaboratorDeleteId: null,
      collaboratorName: null,
      collaboratorEmail: null,
      projectCollaborators: false,
    };
  }

  // This method used for close modal.
  closeModal = () => {
    this.setState({
      showModal: false,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
      collaboratorDeleteId: null,
      collaboratorName: null,
      collaboratorEmail: null,
    });
  };

  // This method used for open modal.
  showModalFunc = () => {
    this.setState({
      showModal: true,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
    });
  };

  // This method used for open collaborator modal.
  showCollaboratorsModal = (item) => {
    if (
      this.props.accessibleFeature.projectCollaborators &&
      this.props.subscriptionStatus !== 'expired'
    ) {
      this.props.setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      });
      const { fetchCollaboratorList } = this.props;
      fetchCollaboratorList(item._id);
      this.setState({
        showModal: false,
        showEdit: false,
        isShowCollaborators: true,
        isShowCollaboratorsDelete: false,
      });
    } else {
      this.props.setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: () => {
          this.showCollaboratorsModal(item);
        },
        feature: 'projectCollaborators',
      });
    }
  };

  // This method used to soft delete project.
  deleteSpecificProject = (item) => {
    const { softDeleteProject } = this.props;
    softDeleteProject(item._id);
  };

  submitEmailForm = async (values, _, props) => {
    const { createCollaborator, fetchCollaboratorList } = this.props;
    const name = get(props, 'item.creator.username', '');
    const projectId = get(props, 'item._id');
    const title = get(props, 'item.cover.title');
    const producerName = get(props, 'item.cover.producer');
    const directorName = get(props, 'item.cover.director');
    const writerName = get(props, 'item.cover.writer');
    const creatorId = get(props, 'item.creator.userId');
    if (projectId) {
      const value = {
        projectCreatorInfo: {
          projectCreatorId: creatorId,
          fullName: name,
        },
        projectInfo: {
          projectsId: projectId,
          title: title,
          producer: producerName,
          director: directorName,
          writer: writerName,
        },
        status: 'pending',
        email: values.inviteEmail,
        profileImage: null,
        fullName: null,
        reminders: [
          {
            date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
          },
        ],
      };
      await createCollaborator(value);
    }
    await fetchCollaboratorList(projectId);
  };

  // Send reminder to collaborators.
  sendReminder = async (email, projectId) => {
    const { sendReminderToCollaborator, fetchCollaborator } = this.props;
    let collaboratorData = null;
    if (email && projectId) {
      collaboratorData = await fetchCollaborator({
        email: encodeURIComponent(email),
        id: projectId,
      });
      const data = collaboratorData ? collaboratorData[0] : false;
      const id = await get(data, '_id');
      const oldDates = await get(data, 'reminders');
      const reninderNewDate = {
        date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
      };
      oldDates.push(reninderNewDate);
      if (data) {
        const value = {
          projectCreatorInfo: {
            projectCreatorId: data.projectCreatorInfo.projectCreatorId,
            fullName: get(data, 'projectCreatorInfo.fullName', ''),
          },
          projectInfo: {
            projectsId: projectId,
            title: data.projectInfo.title,
            producer: data.projectInfo.producer,
            director: data.projectInfo.director,
            writer: data.projectInfo.writer,
          },
          status: 'pending',
          email: data.email,
          profileImage: null,
          fullName: null,
          reminders: oldDates,
        };
        sendReminderToCollaborator(value, id);
      }
    }
  };

  deleteSpecificCollaborator = (item) => {
    const { deleteCollaborator, removeSectionItems } = this.props;
    const { collaboratorDeleteId, collaboratorEmail } = this.state;
    const projectCollaborators = get(item, 'projectCollaborator');
    const isRemoveItem = filter(projectCollaborators, {
      email: collaboratorEmail,
    });

    if (isRemoveItem.length > 0) {
      const removeItemId = isRemoveItem[0]._id;
      removeSectionItems(item._id, 'projectCollaborator', removeItemId);
    }
    this.closeModal();
    deleteCollaborator(collaboratorDeleteId);
  };

  isDeleteCollaborator = (id, name, email) => {
    this.setState({
      showModal: false,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: true,
      collaboratorDeleteId: id,
      collaboratorName: name,
      collaboratorEmail: email,
    });
  };

  handleOpenProject = (item) => {
    const { index, accessibleFeature, setSubscriptionJourneyModal } =
      this.props;
    if (
      (index > 1 && !accessibleFeature.unlimitedProjects) ||
      (index > 1 && this.props.subscriptionStatus === 'expired')
    ) {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.handleOpenProject,
        feature: 'unlimitedProjects',
      });
    } else {
      Router.push(
        '/project/overview/[homePage]',
        `/project/overview/${item._id}`,
      );
    }
  };
  handleOpenModal = () => {
    const { index, accessibleFeature, setSubscriptionJourneyModal } =
      this.props;
    if (
      (index > 1 && !accessibleFeature.unlimitedProjects) ||
      (index > 1 && this.props.subscriptionStatus === 'expired')
    ) {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.handleOpenModal,
        feature: 'unlimitedProjects',
      });
    } else {
      this.setState({
        showEdit: !this.state.showEdit,
      });
    }
  };

  render() {
    const {
      showEdit,
      showModal,
      isShowCollaborators,
      isShowCollaboratorsDelete,
      collaboratorName,
      projectCollaborators,
    } = this.state;
    const { item, userId, accessibleFeature, index } = this.props;

    return (
      <div>
        <Modal
          modalShow={showModal}
          title="Are you sure you want to delete this project?"
          body="It will be relocated to the Recently Removed Projects section for 30 days before being permanently removed."
          closeCallback={() => this.closeModal()}
          closeBtnText="CANCEL"
          successCallback={() => this.deleteSpecificProject(item)}
          successBtnText="DELETE"
          titleClass={`${Style.modalTitle} m-0`}
          modalSize="custom"
          closeBtnClass="cancelButton"
        />
        <Modal
          modalShow={isShowCollaborators}
          title="Collaborators"
          body={
            <Collaborators
              projectId={item._id}
              collaboratorList={this.props.collaboratorList}
              handleSubmit={this.props.handleSubmit}
              submitEmailForm={this.submitEmailForm}
              sendReminder={this.sendReminder}
              isDeleteCollaborator={this.isDeleteCollaborator}
              t={this.props.t}
            />
          }
          closeCallback={() => this.closeModal()}
          titleClass={`${Style.modalTitle}`}
          modalSize="xl"
          className={style.inviteModal}
          modalHeader={style.modalHeader}
          svgIconClass={style.svgIconClass}
          modalFooter={style.modalFooter}
        />
        <Modal
          modalShow={isShowCollaboratorsDelete}
          title="Remove collaborator?"
          body={`Are you sure you want to remove ${collaboratorName} as a collaborator? They won’t be able to access this project anymore.`}
          closeCallback={() => this.closeModal()}
          closeBtnText="CANCEL"
          successCallback={() => this.deleteSpecificCollaborator(item)}
          successBtnText="DELETE"
          titleClass={`${Style.modalTitle}`}
          closeBtnClass="cancelButton"
          className={style.inviteModal}
          svgIconClass={style.svgIconClass}
          modalSize="xl"
          modalFooter={style.footerRightAlign}
          bodyClass={style.bodyClass}
        />
        <div
          className={`${Style.iconPosition}`}
          onClick={() => this.handleOpenModal('')}
        >
          {(index > 1 && !accessibleFeature.unlimitedProjects) ||
          (index > 1 && this.props.subscriptionStatus === 'expired') ? (
            <LockIcon height={24} width={24} show={true} viewbox="0 0 24 24" />
          ) : !showEdit ? (
            <Icon
              icon={EditIconSvg}
              color="#00000"
              viewBox="32"
              iconSize="16px"
            />
          ) : (
            <Icon icon={CrossSvg} color="#00000" viewBox="32" iconSize="26px" />
          )}
        </div>
        {showEdit && (
          <div className={`${Style.dropdownPos}`}>
            <div className={`${Style.dropDownContent} p-0`}>
              <div onClick={() => this.handleOpenProject(item)}>
                <p
                  className={`${Style.dropDownOptions} p2 text-primary mt-12 mb-0`}
                >
                  EDIT
                </p>
              </div>
              {userId === get(item, 'creator.userId') && (
                <div
                  onClick={() => {
                    if (!projectCollaborators) {
                      this.showCollaboratorsModal(item);
                    } else {
                      this.props.handleSubscriptionModal(
                        'projectCollaborators',
                      );
                    }
                  }}
                >
                  <p
                    className={`${Style.dropDownOptions} p2 text-primary d-flex mb-0`}
                  >
                    {'COLLABORATE'}{' '}
                    {
                      <span className={Style.lockIconDiv}>
                        <LockIcon
                          height={17}
                          width={16}
                          show={
                            !accessibleFeature.projectCollaborators ||
                            this.props.subscriptionStatus === 'expired'
                          }
                        />
                      </span>
                    }
                  </p>
                </div>
              )}
              {userId === get(item, 'creator.userId') && (
                <div onClick={() => this.showModalFunc(item)}>
                  <p
                    className={`${Style.dropDownOptions} p2 text-primary mb-12`}
                  >
                    DELETE
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
}
ExisitingProject.defaultProps = {};

ExisitingProject.propTypes = {
  item: PropTypes.func.isRequired,
  deleteProject: PropTypes.func.isRequired,
  collaboratorList: PropTypes.array.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  t: PropTypes.object.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  userId: PropTypes.string.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
};

export default reduxForm({
  form: 'inviteForm',
})(withTranslation('common')(ExisitingProject));
