import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Router from 'next/router';
import { connect } from 'react-redux';
import { isEmpty, get } from 'lodash';
import {
  getPlans,
  getSubscription,
  setBillingCycle,
  setSubscriptionJourneyModal,
  setReminder,
  getCampaignDetails,
} from 'reducer/subscription';
import { setCachedRoute } from 'reducer/auth';
import DashboardHeader from 'sharedComponents/Header/dashboardHeader';
import ManageDiscount from './manageDiscount';

function Index({
  userData,
  billingCycle,
  freePlan,
  enterPrisePlan,
  getPlans,
  setBillingCycle,
  setCachedRoute,
  token,
  getSubscription,
  setReminder,
  getCampaignDetails,
  setShowPaymentScreen
}) {
  const [selectedPlanDetalils, setSelectedPlanDetails] = useState('');
  const [campaignDetails, setCampaignDetails] = useState('');
  const [proPlan, setProPlan] = useState(null);

  function getPriceByBillingCycle() {
    const productPrice = get(proPlan, 'productPrice', []);
    if (!isEmpty(productPrice)) {
      const result = productPrice.find(
        (price) => price.billing === billingCycle,
      );
      setSelectedPlanDetails(result);
    }
  }

  useEffect(() => {
    const fetchData = async () => {
      const currentPath = Router.pathname;
      if (!token) {
        setCachedRoute(Router.asPath);
        return;
      }
      setCachedRoute('');
      const { routeToken } = Router.query;
      try {
        const data = await getSubscription(currentPath);
        // if (get(data, 'userMeta.type', null) !== 'legacy') {
        //   Router.push('/');
        // }
      } catch (err) {
        console.log(err);
      }
      getPlans();
      let campaignDetails = {};
      if (routeToken) {
        try {
          /************For get campaigne details*******************/
          const data = await getCampaignDetails(routeToken);
          campaignDetails = get(data, 'metadata', {});
          setProPlan(get(data, 'plan', {}));
        } catch (error) {
          console.error('Error decoding token:', error);
        }
      }

      if (!isEmpty(campaignDetails)) {
        const { priceId, planId, couponCode, billingCycle } = campaignDetails;
        if (priceId && planId && couponCode && billingCycle) {
          setReminder(false);
          setCampaignDetails(campaignDetails);
          setBillingCycle(billingCycle);
        }
      }
    };

    fetchData();

    return () => {
      setBillingCycle('monthly');
    };
  }, []);
  // Runs only once on mount

  // Run this when proPlan or billingCycle changes, but prevent loops
  useEffect(() => {
    if (!isEmpty(proPlan)) {
      getPriceByBillingCycle();
    }
  }, [proPlan, billingCycle]); // Add dependencies carefully

  return (
    <>
      <Head>
        <title>Offer | Smash</title>
        <link rel="shortcut icon" href="/favicon.ico" />
      </Head>

      <DashboardHeader
        profile={userData.profile}
        // IconPath={BackIconSvg}
        clickHandler={() => {
          Router.push('/profile/getStarted');
        }}
      />
      <ManageDiscount
        heading={'Smash Pro Annual Discount'}
        subHeading={
          'As a thank you for being an early adopter of Smash we are offering Smash Legacy users a year of Smash Pro for the price of one month.'
        }
        proPlan={proPlan}
        selectedPlanDetalils={selectedPlanDetalils}
        enterPrisePlan={enterPrisePlan}
        setShowPaymentScreen={setShowPaymentScreen}
        freePlan={freePlan}
        campaignDetails={campaignDetails}
        billingCycle={billingCycle}
      />
    </>
  );
}

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  billingCycle: state.subscription.billingCycle,
  subscriptionJourney: state.subscription.subscriptionJourney,
  // proPlan: state.subscription.proPlan,
  enterPrisePlan: state.subscription.enterPrisePlan,
  freePlan: state.subscription.freePlan,
  token: state.auth.token,
});

const mapDispatchToProps = (dispatch) => {
  return {
    getSubscription: (payload) => dispatch(getSubscription(payload)),
    setShowPaymentScreen: (payload) => dispatch(getSubscription(payload)),
    getPlans: (payload) => dispatch(getPlans(payload)),
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
    setBillingCycle: (payload) => dispatch(setBillingCycle(payload)),
    setCachedRoute: (payload) => dispatch(setCachedRoute(payload)),
    setReminder: (payload) => dispatch(setReminder(payload)),
    getCampaignDetails: (payload) => dispatch(getCampaignDetails(payload)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Index);
