import React from 'react';
import Styles from '../style/discount.module.css';
import PaymentScreen from 'sharedComponents/PaymentScreen/paymentScreen';

function ManageDiscount({
  proPlan,
  selectedPlanDetalils,
  enterPrisePlan,
  freePlan,
  campaignDetails,
  billingCycle,
  subHeading,
  heading,
  setShowPaymentScreen
}) {
  return (
    <div className="justify-content-center row m-0 p-0">
      <div
        className={`${Styles.discountDiv} col-12 col-sm-12 col-md-9 col-lg-9`}
      >
        <div className={`${Styles.title} row justify-content-center m-0 p-0`}>
          <p className={`${Styles.heading} mb-0 col-12 col-md-9 col-lg-9`}>
            {heading}
          </p>
          <p className={`${Styles.subHeading} mb-0 col-12 col-md-9 col-lg-9`}>
            {subHeading}
          </p>
        </div>
        <div className="col-12">
          <PaymentScreen
            plan={'pro'}
            proPlan={proPlan}
            setShowPaymentScreen={setShowPaymentScreen}
            selectedPlanDetalils={selectedPlanDetalils}
            enterPrisePlan={enterPrisePlan}
            freePlan={freePlan}
            campaignDetails={campaignDetails}
            billingCycle={billingCycle}
          />
        </div>
      </div>
    </div>
  );
}

export default ManageDiscount;
