import {
  Create,
  SimpleForm,
  TextInput,
  ReferenceInput,
  AutocompleteInput,
  useRedirect,
  useNotify,
  useRefresh,
  useDataProvider,
} from 'react-admin';
import { Add } from '@mui/icons-material';
import { Box, Typography, Button } from '@mui/material';
// import LocationInput from '../../sharedComponents/locationInput';
import { get } from 'lodash';
import { useState } from 'react';
import ImageUpload from '../../sharedComponents/imageUploader';
import { required } from '../../helpers/commonValidation';
import LocationInput from '../../sharedComponents/locationInput';

const CreateCallOut = (props) => {
  const [discovererData, setDiscovererData] = useState({
    userName: '',
    organisationName: '',
    email: '',
    city: '',
    organisationBio: '',
    organisationLogo: '',
  });

  const [isSelected, setIsSelected] = useState(null);
  const [location, setLocation] = useState(null);

  const redirect = useRedirect();
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();

  const handleUserChange = async (value, record) => {
    setDiscovererData(record);
    setIsSelected('updateDiscoverer');
  };

  const handleInputChange = (field, value) => {
    setDiscovererData((prevData) => ({
      ...prevData,
      [field]: value,
    }));
  };

  const handleSave = async (value) => {
    const id = discovererData.id || ''; // If existing user

    // Extract first name and last name dynamically from userName
    const [firstName, ...lastNameParts] = discovererData.userName
      .trim()
      .split(' ');
    const lastName = lastNameParts.join(' ');
    const fullName = discovererData.userName;

    try {
      let callOutId;
      if (id) {
        const payload = {
          profile: {
            name: {
              firstName: firstName || '',
              lastName: lastName || '',
              fullName: fullName,
            },
            organisation:
              discovererData.organisationName !== ''
                ? discovererData.organisationName
                : value.organisationName,
            discovererProfile: discovererData.organisationBio,
            organisationLogo: discovererData.organisationLogo,
            city: discovererData.location,
          },
        };

        // Update existing user
        await dataProvider.update('v1/user', {
          method: 'PATCH',
          id: id,
          data: payload,
        });

        // Create a callout after updating the user
        callOutId = await createCallout(id, discovererData.organisationName !== ''
          ? discovererData.organisationName
          : value.organisationName);

        notify("Discoverer's profile updated successfully", {
          type: 'success',
          multiLine: true,
          autoHideDuration: 2500,
        });
        notify('User updated successfully!');
      } else {
        const payload = {
          email: discovererData.email,
          profile: {
            name: {
              firstName: firstName || '',
              lastName: lastName || '',
              fullName: fullName,
            },
            organisation: discovererData.organisationName,
            discovererProfile: discovererData.organisationBio,
            organisationLogo: discovererData.organisationLogo,
            city: location.city,
          },
        };

        // Create a new user
        const response = await dataProvider.create('v1/user/create', {
          data: { ...payload },
        });

        const newUser = response.data;
        notify('New discoverer created successfully!');

        setDiscovererData(newUser);

        // Create a callout for the new user
        callOutId = await createCallout(newUser.id);
      }

      refresh();
      redirect(`/callouts/${callOutId}/create`);
    } catch (error) {
      console.error('API Error:', error);
      notify('Error saving user. Please try again.');
    }
  };

  const createCallout = async (discovererId, organisationName) => {
    try {
      const companyName = (discovererData.organisationName !== ''
        ? discovererData.organisationName
        : organisationName || '').trim();
      const companyProfileHeading = `<h3>About ${companyName}</h3>`;

      const response = await dataProvider.create('v1/callout', {
        method: 'POST',
        data: {
          name: 'Untitled',
          discovererId: discovererId,
          body: {
            logo: discovererData.organisationLogo || '',
            companyName: companyName,
            companyProfile: discovererData.organisationBio,
            companyProfileHeading: companyProfileHeading,
          },
        },
      });

      return response.data.id;
    } catch (error) {
      console.error('Error creating callout:', error);
      notify('Error creating callout. Please try again.');
      return null;
    }
  };

  return (
    <>
      {!isSelected ? (
        <>
          <Box
            display="flex"
            sx={{ justifyContent: 'flex-end' }}
            my={2}
            width="100%"
          >
            <Button
              startIcon={<Add />}
              variant="contained"
              color="primary"
              sx={{ mt: 1 }}
              onClick={() => setIsSelected('createNew')}
            >
              Create New
            </Button>
          </Box>
          <Create {...props} title="Create Callout">
            <SimpleForm toolbar={false}>
              <ReferenceInput
                debounce={500}
                source="name"
                reference="users"
                fullWidth
                filter={{ noPagination: true, userType: 'ops' }}
              >
                <AutocompleteInput
                  fullWidth
                  optionText="userName"
                  optionValue="id"
                  onChange={handleUserChange}
                />
              </ReferenceInput>
            </SimpleForm>
          </Create>
        </>
      ) : (
        <Create {...props} title="Create Callout">
          <SimpleForm onSubmit={handleSave}>
            <Box width="100%" display="flex" gap={2}>
              <TextInput
                disabled={
                  discovererData.userName !== '' && isSelected !== 'createNew'
                }
                source="userName"
                label="Discoverer Name"
                fullWidth
                validate={required}
                defaultValue={discovererData.userName}
                onChange={(e) => handleInputChange('userName', e.target.value)}
              />
              <TextInput
                disabled={
                  discovererData.organisationName !== '' &&
                  isSelected !== 'createNew'
                }
                source="organisationName"
                label="Organisation Name"
                fullWidth
                validate={required}
                defaultValue={discovererData.organisationName}
                onChange={(e) =>
                  isSelected === 'createNew'
                    ? handleInputChange('organisationName', e.target.value)
                    : () => {}
                }
              />
              <TextInput
                disabled={
                  discovererData.email !== '' && isSelected !== 'createNew'
                }
                source="discovererEmail"
                label="Discoverer Email"
                fullWidth
                type="email"
                validate={required}
                defaultValue={discovererData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
              <LocationInput
                disabled={
                  discovererData.city !== '' && isSelected !== 'createNew'
                }
                id="autocomplete"
                source="discovererLocation"
                label="Discoverer Location"
                setLocation={setLocation}
                fullWidth
                size="large"
                validate={required}
                defaultValue={discovererData.city}
              />
            </Box>

            <Box mt={2} width="100%">
              <Typography mb={2} variant="h6">
                Discoverer Profile
              </Typography>
              <TextInput
                source="profile"
                label=""
                multiline
                fullWidth
                validate={required}
                rows={10}
                defaultValue={discovererData.organisationBio}
                onChange={(e) =>
                  handleInputChange('organisationBio', e.target.value)
                }
              />
            </Box>

            <Box mt={2} width="100%">
              <Typography mb={2} variant="h6">
                Discover Logo Upload
              </Typography>
              <ImageUpload
                fullWidth
                validate={true}
                source="profileImage"
                // label="Discover Logo Upload"
                maxSize={10000000}
                onUploadSuccess={(imageUrl) =>
                  handleInputChange('organisationLogo', imageUrl)
                }
                defaultValue={get(discovererData, 'organisationLogo', '')}
              />
            </Box>
          </SimpleForm>
        </Create>
      )}
    </>
  );
};

export default CreateCallOut;
